const express = require('express');
const router = express.Router();

// GET route to display the listing form
router.get('/list-home', (req, res) => {
  res.send(`
    <h1>List Your Property</h1>
    <form action='/list-home' method='POST'>
      <input type="text" name="name" placeholder="enter name" required><br><br>
      <input type="text" name="location" placeholder="enter location" required><br><br>
      <input type="text" name="price" placeholder="enter price" required><br><br>
      <input type="text" name="description" placeholder="enter description" required><br><br>
      <input type="text" name="image" placeholder="enter image URL"><br><br>
      <input type="text" name="contact" placeholder="enter contact" required><br><br>
      <input type="text" name="amenities" placeholder="enter amenities"><br><br>
      <button type="submit">Submit Listing</button>
    </form>
    <br>
    <a href="/">← Back to Home</a>
  `);
});

// POST route to handle form submission
router.post('/list-home', (req, res) => {
  const formData = req.body;

  console.log('=== NEW LISTING SUBMITTED ===');
  console.log(formData);
  console.log('==============================');

  res.send(`
    <h1>Listing Submitted Successfully!</h1>
    <h2>Property Details:</h2>
    <p><strong>Name:</strong> ${formData.name || 'Not provided'}</p>
    <p><strong>Location:</strong> ${formData.location || 'Not provided'}</p>
    <p><strong>Price:</strong> ${formData.price || 'Not provided'}</p>
    <p><strong>Description:</strong> ${formData.description || 'Not provided'}</p>
    <p><strong>Image:</strong> ${formData.image || 'Not provided'}</p>
    <p><strong>Contact:</strong> ${formData.contact || 'Not provided'}</p>
    <p><strong>Amenities:</strong> ${formData.amenities || 'Not provided'}</p>
    <br>
    <a href="/list-home">← List Another Property</a> |
    <a href="/">← Back to Home</a>
  `);
});

module.exports = router;