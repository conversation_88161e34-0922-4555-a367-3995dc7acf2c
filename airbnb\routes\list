const express = require('express');
const app = express();
const hostrouter = express.Router();



hostrouter.get('/list-home', (req, res) => {
  res.send(`
    <form action='/list-home' method='POST'>
      <input type="text" name="name" placeholder="enter name"><br><br>
      <input type="text" name="location" placeholder="enter location"><br><br>
      <input type="text" name="price" placeholder="enter price"><br><br>
      <input type="text" name="description" placeholder="enter description"><br><br>
      <input type="text" name="image" placeholder="enter image"><br><br>
      <input type="text" name="contact" placeholder="enter contact"><br><br>
      <input type="text" name="amenities" placeholder="enter amenities"><br><br>
      <button type="submit">Submit</button>
    </form>
  `);
});

export default hostrouter;