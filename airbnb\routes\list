const express = require('express');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// Helper function to serve HTML files (same as in main file)
const serveHTML = (filePath, res, data = {}) => {
  const fullPath = path.join(__dirname, '..', 'views', filePath);
  fs.readFile(fullPath, 'utf8', (err, html) => {
    if (err) {
      console.error('Error reading HTML file:', err);
      res.status(500).send('<h1>Internal Server Error</h1>');
      return;
    }

    // Replace placeholders with actual data
    let processedHTML = html;
    Object.keys(data).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = data[key] || 'Not provided';
      processedHTML = processedHTML.replace(new RegExp(placeholder, 'g'), value);
    });

    res.send(processedHTML);
  });
};

// GET route to display the listing form
router.get('/list-home', (req, res) => {
  serveHTML('list-form.html', res);
});

// POST route to handle form submission
router.post('/list-home', (req, res) => {
  const formData = req.body;

  console.log('=== NEW LISTING SUBMITTED ===');
  console.log(formData);
  console.log('==============================');

  // Serve the success page with form data
  serveHTML('list-success.html', res, formData);
});

module.exports = router;