const http=require('http');
const express = require('express');
const fs = require('fs');

const bodyparser = require('body-parser');

const app = express();
const PORT = 3000;

// Middleware to log requests (for all routes)
app.use((req, res, next) => {
  console.log("Request received", req.url, req.method);
  next();
});

// Route handlers
app.get('/', (req, res) => {
  res.send("<h1>Home Page</h1>");
});

app.get('/about', (req, res) => {
  res.send("<h1>About Page</h1>");
});

app.get('/form', (req, res) => {
  res.send(`
    <h1>Contact Page</h1>
    <form action="/form" method="POST">
        <div>
            <label for="name">Name:</label><br>
            <input type="text" id="name" name="name" required><br><br>
        </div>
        <div>
            <label for="email">Email:</label><br>
            <input type="email" id="email" name="email" required><br><br>
        </div>
        <div>
            <label for="message">Message:</label><br>
            <textarea id="message" name="message" rows="4" cols="50" required></textarea><br><br>
        </div>
        <button type="submit">Submit</button>
    </form>
` );
});

app.use(bodyparser.urlencoded());

app.post('/form', (req, res) => {
  const formData = req.body;
 
  console.log(formData);
  
  
  res.send(`
    <h1>Form Submitted Successfully!</h1>
    <h2>Received Data:</h2>
`);
});




app.listen(PORT, () => {
  console.log(`Server is running on port http://localhost:${PORT}`);
});
