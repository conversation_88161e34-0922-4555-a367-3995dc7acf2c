const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const PORT = 3000;

// Import routes
const listRoutes = require('./routes/list');

// Helper function to serve HTML files
const serveHTML = (filePath, res, data = {}) => {
  const fullPath = path.join(__dirname, 'views', filePath);
  fs.readFile(fullPath, 'utf8', (err, html) => {
    if (err) {
      console.error('Error reading HTML file:', err);
      res.status(500).send('<h1>Internal Server Error</h1>');
      return;
    }

    // Replace placeholders with actual data
    let processedHTML = html;
    Object.keys(data).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = data[key] || 'Not provided';
      processedHTML = processedHTML.replace(new RegExp(placeholder, 'g'), value);
    });

    res.send(processedHTML);
  });
};

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware to log requests
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Main routes
app.get('/', (req, res) => {
  serveHTML('home.html', res);
});

// Use route modules
app.use('/', listRoutes);

// 404 handler
app.use((req, res) => {
  res.status(404);
  serveHTML('404.html', res);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Airbnb server is running on http://localhost:${PORT}`);
});
