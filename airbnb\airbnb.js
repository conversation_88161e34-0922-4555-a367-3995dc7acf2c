const express = require('express');
const app = express();
const PORT = 3000;

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.get('/', (req, res) => {
  res.send("<h1>welcome to airbnb</h1>");
});



app.post('/list-home', (req, res) => {
  const formData = req.body;
 
  console.log(formData);
  
  
  res.send(`
    <h1>Form Submitted Successfully!</h1>
    <h2>Received Data:</h2>
    <p><strong>Name:</strong> ${formData.name || 'Not provided'}</p>
    <p><strong>Location:</strong> ${formData.location || 'Not provided'}</p>
    <p><strong>Price:</strong> ${formData.price || 'Not provided'}</p>
    <p><strong>Description:</strong> ${formData.description || 'Not provided'}</p>
    <p><strong>Image:</strong> ${formData.image || 'Not provided'}</p>
    <p><strong>Contact:</strong> ${formData.contact || 'Not provided'}</p>
    <p><strong>Amenities:</strong> ${formData.amenities || 'Not provided'}</p>
    <br>
    <a href="/list-home">← Go back to form</a>
  `);
});


app.listen(PORT, () => {
  console.log(`Server is running on port http://localhost:${PORT}`);
});
