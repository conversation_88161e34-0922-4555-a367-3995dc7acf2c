const express = require('express');
const app = express();
const PORT = 3000;

app.use(express.json());
app.use(express.urlencoded())

app.get('/', (req, res) => {
  res.send("<h1>welcome to airbnb</h1>");
});

app.get('/list home', (req, res) => {
  res.send("<form action='/list home' method='POST'>
<input type="text" name="name" placeholder="enter name">
<input type="text" name="location" placeholder="enter location">
<input type="text" name="price" placeholder="enter price">
<input type="text" name="description" placeholder="enter description">
<input type="text" name="image" placeholder="enter image">
<input type="text" name="contact" placeholder="enter contact">
<input type="text" name="amenities" placeholder="enter amenities">
 </form>");
 });

 app.post('/list home', (req, res) => {
  const formData = req.body;
 
  console.log(formData);
  
  
  res.send(`
    <h1>Form Submitted Successfully!</h1>
    <h2>Received Data:</h2>
`);
});


app.listen(PORT, () => {
  console.log(`Server is running on port http://localhost:${PORT}`);
});
