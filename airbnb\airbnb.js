const express = require('express');
const app = express();
const PORT = 3000;

// Import routes
const listRoutes = require('./routes/list');

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware to log requests
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Main routes
app.get('/', (req, res) => {
  res.send(`
    <h1>Welcome to Airbnb Clone</h1>
    <p>Your home away from home</p>
    <br>
    <a href="/list-home">📝 List Your Property</a>
    <br><br>
    <a href="/search">🔍 Search Properties</a>
  `);
});

// Use route modules
app.use('/', listRoutes);

// 404 handler
app.use((req, res) => {
  res.status(404).send(`
    <h1>404 - Page Not Found</h1>
    <p>The page you're looking for doesn't exist.</p>
    <a href="/">← Back to Home</a>
  `);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Airbnb server is running on http://localhost:${PORT}`);
});
