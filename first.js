const http=require('http');
const fs = require('fs');

const server = http.createServer((req,res)=>{
    console.log("Request received",req.url,req.method);
    console.log(req.url,req.method,req.headers);
    res.setHeader('Content-Type','text/html');

    if(req.url==='/home'){
        res.write('<h1>Home Page</h1>');
        return res.end();
    }else if(req.url==='/about'){
        res.write('<h1>About Page</h1>');
        return res.end();
    }else if(req.url==='/contact' && req.method === 'GET'){
        // Display contact form
        res.write(`
            <h1>Contact Page</h1>
            <form action="/contact" method="POST">
                <div>
                    <label for="name">Name:</label><br>
                    <input type="text" id="name" name="name" required><br><br>
                </div>
                <div>
                    <label for="email">Email:</label><br>
                    <input type="email" id="email" name="email" required><br><br>
                </div>
                <div>
                    <label for="message">Message:</label><br>
                    <textarea id="message" name="message" rows="4" cols="50" required></textarea><br><br>
                </div>
                <button type="submit">Submit</button>
            </form>
        `);
        return res.end();
    }else if(req.url==='/contact' && req.method === 'POST'){
        // Handle form submission
        let body = '';

        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            console.log('=== FORM PAYLOAD ===');
            console.log('Raw payload:', body);

            // Parse the form data
            const formData = new URLSearchParams(body);
            const parsedData = {};
            for (const [key, value] of formData) {
                parsedData[key] = value;
            }

            console.log('Parsed data:', parsedData);
            console.log('==================');

            fs.writeFile('form-data.json', JSON.stringify(parsedData, null, 2), (err) => {
                if (err) throw err;
                console.log('Data written to file');
            });

            // Send response back to user
            res.write(`
                <h1>Form Submitted Successfully!</h1>
                <h2>Received Data:</h2>
                <p><strong>Name:</strong> ${parsedData.name || 'Not provided'}</p>
                <p><strong>Email:</strong> ${parsedData.email || 'Not provided'}</p>
                <p><strong>Message:</strong> ${parsedData.message || 'Not provided'}</p>
                <br>
                <h3>Raw Payload (check console):</h3>
                <pre>${body}</pre>
                <br>
                <a href="/contact">← Go back to form</a>
            `);
            res.end();
        });

        return;
    }else{
        res.write('<h1>404 Page Not Found</h1>');
        return res.end();
    }
});

const PORT=3002;
server.listen(PORT,()=>{
    console.log(`server is running on port http://localhost:${PORT}`);

})