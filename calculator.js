const http = require('http');
const fs = require('fs');

const server = http.createServer((req, res) => {
  console.log(req.url, req.method, req.headers);
  res.setHeader('Content-Type', 'text/html');

  if(req.url='/conatact' &&req.method=='GET'){
    res.writeHead(<h1>Calculator</h1>)
    res.write(<body>
        <input type='number' placeholder='enter number 1' name="num1"></input>
        <input type="number" placeholder="enter number two" name="num2"> </input>
        <button type='submit' name="submit"> calculate</button></body>)
        res.end();
        }
        else if(req.url=='/conatact' && req.method=='POST'){
            let body = '';

            req.on('data', chunk => {
              body += chunk.toString();
            });
      
            req.on('end', () => {
              console.log('=== FORM PAYLOAD ===');
              console.log('Raw payload:', body);
      
              // Parse the form data
              const formData = new URLSearchParams(body);
              const parsedData = {};
              for (const [key, value] of formData) {
                parsedData[key] = value;
              }
      
              console.log('Parsed data:', parsedData);
              console.log('==================');
      
              // Perform calculation
              const num1 = parseFloat(parsedData.num1);
              const num2 = parseFloat(parsedData.num2);
              const result = num1 + num2;
      
              // Send response back to user
              res.write(`
                <h1>Calculation Result</h1>
                <p>${num1} + ${num2} = ${result}</p>
                <br>
                <a href="/contact">← Go back to form</a>
              `);
              res.end();
            });
      
            return;


  }
});

const PORT = 3002;
server.listen(PORT, () => {
  console.log(`Server is running on port http://localhost:${PORT}`);
});



